const postgres = require('postgres');
const { env } = require('./lib/env.mjs');

const sql = postgres(env.DATABASE_URL);

async function viewDatabase() {
  try {
    console.log('🔍 查看数据库信息...\n');
    
    // 1. 查看所有表
    console.log('📋 数据库中的表：');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `;
    
    if (tables.length === 0) {
      console.log('   没有找到任何表');
      return;
    }
    
    tables.forEach((table, index) => {
      console.log(`   ${index + 1}. ${table.table_name}`);
    });
    
    console.log('\n');
    
    // 2. 查看每个表的详细信息
    for (const table of tables) {
      const tableName = table.table_name;
      console.log(`📊 表 "${tableName}" 的信息：`);
      
      // 查看表结构
      const columns = await sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = ${tableName}
        ORDER BY ordinal_position
      `;
      
      console.log('   列信息：');
      columns.forEach(col => {
        console.log(`     - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(必填)' : '(可选)'}`);
      });
      
      // 查看数据数量
      const countResult = await sql`SELECT COUNT(*) as count FROM ${sql(tableName)}`;
      const count = countResult[0].count;
      console.log(`   数据行数: ${count} 条`);
      
      // 如果有数据，显示前几条
      if (count > 0) {
        const sampleData = await sql`SELECT * FROM ${sql(tableName)} LIMIT 3`;
        console.log('   示例数据（前3条）：');
        sampleData.forEach((row, index) => {
          console.log(`     第${index + 1}条:`);
          Object.entries(row).forEach(([key, value]) => {
            if (key === 'embedding') {
              console.log(`       ${key}: [向量数据，长度: ${value ? value.length : 0}]`);
            } else {
              const displayValue = typeof value === 'string' && value.length > 100 
                ? value.substring(0, 100) + '...' 
                : value;
              console.log(`       ${key}: ${displayValue}`);
            }
          });
        });
      }
      
      console.log('\n' + '='.repeat(50) + '\n');
    }
    
  } catch (error) {
    console.error('❌ 查看数据库时出错:', error.message);
  } finally {
    await sql.end();
  }
}

viewDatabase();
