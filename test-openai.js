const OpenAI = require('openai');

// 直接使用配置，避免 env 文件问题
const client = new OpenAI({
  apiKey: '********************************************************************************************************************************************************************',
  baseURL: 'https://api.openai.com/v1',
});

async function testOpenAI() {
  try {
    console.log('🔍 测试 OpenAI API 连接...\n');

    // 测试 embedding
    console.log('🔤 测试 embedding 模型...');
    const embeddingResponse = await client.embeddings.create({
      model: 'text-embedding-3-small',
      input: '这是一个测试文本'
    });

    console.log('✅ Embedding 模型测试成功!');
    console.log(`向量维度: ${embeddingResponse.data[0].embedding.length}`);
    console.log(`向量前5个值: [${embeddingResponse.data[0].embedding.slice(0, 5).join(', ')}...]`);
    console.log('\n');

    // 测试对话模型
    console.log('🤖 测试对话模型...');
    const chatResponse = await client.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'user',
          content: '你好，请简单介绍一下你自己。'
        }
      ],
      max_tokens: 100
    });

    console.log('✅ 对话模型测试成功!');
    console.log('回复:', chatResponse.choices[0].message.content);

  } catch (error) {
    console.error('❌ API 测试失败:');
    console.error('错误信息:', error.message);
    
    if (error.status === 401) {
      console.log('\n💡 解决方案: 请检查 API Key 是否正确设置');
    }
  }
}

// 直接运行测试
testOpenAI();
