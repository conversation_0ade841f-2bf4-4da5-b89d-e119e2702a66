const OpenAI = require('openai');

// 直接使用配置，避免 env 文件问题
const client = new OpenAI({
  apiKey: 'your-openai-api-key-here', // 请替换为你的实际 API 密钥
  baseURL: 'https://api.openai.com/v1',
});

async function testOpenAI() {
  try {
    console.log('🔍 测试 OpenAI API 连接...\n');

    // 测试 embedding
    console.log('🔤 测试 embedding 模型...');
    const embeddingResponse = await client.embeddings.create({
      model: 'text-embedding-ada-002',
      input: '这是一个测试文本'
    });

    console.log('✅ Embedding 模型测试成功!');
    console.log(`向量维度: ${embeddingResponse.data[0].embedding.length}`);
    console.log(`向量前5个值: [${embeddingResponse.data[0].embedding.slice(0, 5).join(', ')}...]`);
    console.log('\n');

    // 测试对话模型
    console.log('🤖 测试对话模型...');
    const chatResponse = await client.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'user',
          content: '你好，请简单介绍一下你自己。'
        }
      ],
      max_tokens: 100
    });

    console.log('✅ 对话模型测试成功!');
    console.log('回复:', chatResponse.choices[0].message.content);

  } catch (error) {
    console.error('❌ API 测试失败:');
    console.error('错误信息:', error.message);
    
    if (error.status === 401) {
      console.log('\n💡 解决方案: 请检查 API Key 是否正确设置');
    }
  }
}

// 只有在提供了有效 API 密钥时才运行测试
if (process.argv[2]) {
  // 使用命令行参数提供的 API 密钥
  client.apiKey = process.argv[2];
  testOpenAI();
} else {
  console.log('请提供 OpenAI API 密钥:');
  console.log('node test-openai.js your-api-key-here');
}
