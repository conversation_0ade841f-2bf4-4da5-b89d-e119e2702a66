{"name": "private-component-codegen", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "db:generate": "drizzle-kit generate", "db:migrate": "tsx lib/db/migrate.ts", "db:push": "drizzle-kit push", "openai:embedding": "tsx app/api/openai/embedDocs.ts", "llamaindex:embedding": "tsx app/api/llamaindex/embedDocs.ts", "langchain:embedding": "tsx app/api/langchain/embedDocs.ts", "vercelai:embedding": "tsx app/api/vercelai/embedDocs.ts"}, "dependencies": {"@ai-sdk/openai": "^1.0.5", "@ant-design/icons": "^5.5.1", "@ant-design/nextjs-registry": "^1.0.1", "@ant-design/use-emotion-css": "1.0.4", "@emotion/cache": "^11.13.1", "@emotion/css": "^11.13.4", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@langchain/community": "^0.3.16", "@langchain/core": "^0.3.19", "@langchain/openai": "^0.3.14", "@t3-oss/env-nextjs": "^0.10.1", "@tldraw/tldraw": "2.0.0-alpha.17", "ai": "^4.0.9", "antd": "^5.21.5", "canvas-size": "^1.2.6", "drizzle-orm": "^0.31.2", "drizzle-zod": "^0.5.1", "https-proxy-agent": "^7.0.5", "langchain": "^0.3.6", "llamaindex": "^0.8.22", "lodash": "^4.17.21", "nanoid": "^5.0.8", "next": "14.2.3", "openai": "^4.71.1", "pg": "^8.13.1", "postgres": "^3.4.4", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "rehype-katex": "^6.0.3", "remark-breaks": "^3.0.3", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "styled-components": "^6.1.13", "zod": "^3.23.8"}, "devDependencies": {"@chromatic-com/storybook": "^1.9.0", "@storybook/addon-essentials": "^8.3.6", "@storybook/addon-interactions": "^8.3.6", "@storybook/addon-links": "^8.3.6", "@storybook/addon-onboarding": "^8.3.6", "@storybook/addon-styling": "^1.3.7", "@storybook/addon-styling-webpack": "^1.0.0", "@storybook/blocks": "^8.3.6", "@storybook/nextjs": "^8.3.6", "@storybook/react": "^8.3.6", "@storybook/test": "^8.3.6", "@types/canvas-size": "^1.2.2", "@types/lodash": "^4.14.191", "@types/node": "^20", "@types/pg": "^8.11.10", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.6", "autoprefixer": "^10.4.20", "dotenv": "^16.4.5", "drizzle-kit": "^0.22.7", "eslint": "^8", "eslint-config-next": "15.0.1", "eslint-plugin-storybook": "^0.10.1", "postcss": "^8", "storybook": "^8.3.6", "tailwindcss": "^3.4.1", "tsx": "^4.15.7", "typescript": "^5"}}