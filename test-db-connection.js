const postgres = require('postgres');

const sql = postgres('postgresql://postgres.zgqncskcvdavykupphvg:<EMAIL>:6543/postgres');

async function testConnection() {
  try {
    console.log('Testing database connection...');
    const result = await sql`SELECT version()`;
    console.log('✅ Database connection successful');
    console.log('PostgreSQL version:', result[0].version);
    await sql.end();
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Error code:', error.code);
    console.error('Error severity:', error.severity);
    console.error('Full error:', error);
  }
}

testConnection();
