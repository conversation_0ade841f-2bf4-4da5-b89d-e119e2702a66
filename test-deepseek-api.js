const OpenAI = require('openai');
const { env } = require('./lib/env.mjs');

console.log('🔍 测试 DeepSeek API 连接...\n');

const client = new OpenAI({
  apiKey: env.AI_KEY,
  baseURL: env.AI_BASE_URL,
});

async function testAPI() {
  try {
    console.log('配置信息:');
    console.log(`- API Base URL: ${env.AI_BASE_URL}`);
    console.log(`- Model: ${env.MODEL}`);
    console.log(`- Embedding Model: ${env.EMBEDDING}`);
    console.log(`- API Key: ${env.AI_KEY.substring(0, 10)}...`);
    console.log('\n');

    // 测试对话模型
    console.log('🤖 测试对话模型...');
    const chatResponse = await client.chat.completions.create({
      model: env.MODEL,
      messages: [
        {
          role: 'user',
          content: '你好，请简单介绍一下你自己。'
        }
      ],
      max_tokens: 100
    });

    console.log('✅ 对话模型测试成功!');
    console.log('回复:', chatResponse.choices[0].message.content);
    console.log('\n');

    // 测试 embedding 模型
    console.log('🔤 测试 embedding 模型...');
    const embeddingResponse = await client.embeddings.create({
      model: env.EMBEDDING,
      input: '这是一个测试文本'
    });

    console.log('✅ Embedding 模型测试成功!');
    console.log(`向量维度: ${embeddingResponse.data[0].embedding.length}`);
    console.log(`向量前5个值: [${embeddingResponse.data[0].embedding.slice(0, 5).join(', ')}...]`);

  } catch (error) {
    console.error('❌ API 测试失败:');
    console.error('错误信息:', error.message);
    
    if (error.status) {
      console.error('状态码:', error.status);
    }
    
    if (error.error) {
      console.error('详细错误:', error.error);
    }

    // 提供解决建议
    console.log('\n💡 可能的解决方案:');
    if (error.message.includes('model')) {
      console.log('- 检查模型名称是否正确');
      console.log('- 确认你的 API 服务支持该模型');
    }
    if (error.status === 401) {
      console.log('- 检查 API Key 是否正确');
      console.log('- 确认 API Key 是否有效且未过期');
    }
    if (error.status === 404) {
      console.log('- 检查 API Base URL 是否正确');
      console.log('- 确认 API 端点是否可访问');
    }
  }
}

testAPI();
