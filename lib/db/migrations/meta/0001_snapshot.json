{"id": "b2d4f15f-02bd-4227-a486-a6067194e9b9", "prevId": "ab270498-0532-4efe-aac3-bb80ffc07ce2", "version": "7", "dialect": "postgresql", "tables": {"public.open_ai_embeddings": {"name": "open_ai_embeddings", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": true}}, "indexes": {"open_ai_embedding_index": {"name": "open_ai_embedding_index", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.vercel_ai_embeddings": {"name": "vercel_ai_embeddings", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": true}}, "indexes": {"vercel_ai_embedding_index": {"name": "vercel_ai_embedding_index", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}