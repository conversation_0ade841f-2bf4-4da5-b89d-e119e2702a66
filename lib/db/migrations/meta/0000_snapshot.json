{"id": "ab270498-0532-4efe-aac3-bb80ffc07ce2", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.resources": {"name": "resources", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}